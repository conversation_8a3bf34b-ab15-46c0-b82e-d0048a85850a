import { FastifyBaseLogger, FastifyPluginAsync } from 'fastify';
import { EmailParser } from '../services/email-parser.js';
import { queueWebhookDelivery } from '../services/queue.js';
import { prisma } from '../lib/prisma.js';
import { checkUserUsageLimit, incrementUserEmailUsage } from '../lib/auth.js';
import { webSocketService } from '../services/websocket.service.js';

// This route will be called by Postfix when an email arrives
export const emailRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' }; // Reference to global schema

  // Endpoint for processing incoming emails (called by mail server)
  // This corresponds to /webhook/email in openapi-spec.ts
  fastify.post('/process', {
    schema: {
      description: 'Endpoint for Postfix (or other MTA) to forward emails to. This typically requires IP whitelisting or a secret token in the URL/header, not standard user API keys.',
      tags: ['Webhook'],
      summary: 'Receive incoming email (via MTA)',
      body: {
        // This reflects the `message/rfc822` part of the OpenAPI spec
        // Fastify handles raw body as string or buffer if content type matches.
        // For Swagger, we describe it as a binary string.
        type: 'string',
        format: 'binary', // Or just 'string' if specific format isn't critical for docs
        description: 'Raw email content (e.g., RFC 822 format)',
      },
      response: {
        202: {
          description: 'Email accepted for processing.',
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            messageId: { type: 'string', example: '<<EMAIL>>' },
            status: { type: 'string', example: 'queued' },
            emailId: { type: 'string', format: 'uuid' },
          },
        },
        400: { description: 'Invalid email data or missing data', ...errorResponseSchema },
        403: { description: 'Domain not verified or inactive', ...errorResponseSchema },
        404: { description: 'Domain not configured or no webhook for address', ...errorResponseSchema },
        500: { description: 'Internal server error processing email', ...errorResponseSchema },
      },
    },
  }, async (request, reply) => {
    try {
      const rawEmail = request.body as string | Buffer;
      
      if (!rawEmail || rawEmail.length === 0) {
        return reply.code(400).send({ statusCode: 400, error: 'Bad Request', message: 'No email data provided' });
      }

      // Extract target domain from the raw email headers first
      const targetDomain = extractDomainFromRawEmail(rawEmail);
      
      if (!targetDomain) {
        fastify.log.warn('No target domain found in email headers');
        return reply.code(400).send({ statusCode: 400, error: 'Bad Request', message: 'Invalid recipient domain in email headers' });
      }

      // Security check: Only process emails for verified domains
      const domainConfig = await prisma.domain.findUnique({
        where: { domain: targetDomain },
        include: { 
          user: {
            select: {
              id: true,
              currentMonthEmails: true,
              monthlyEmailLimit: true,
            }
          },
          aliases: { 
            include: { webhook: true },
            where: { active: true }
          },
          webhook: true // Domain's default webhook
        },
      });

      if (!domainConfig) {
        fastify.log.info({ domain: targetDomain }, 'Domain not configured');
        return reply.code(404).send({ statusCode: 404, error: 'Not Found', message: 'Domain not configured' });
      }

      // Critical security check: Domain must be verified to receive emails
      if (!domainConfig.verified ) { // Corrected: Prisma model likely uses 'verified'
        fastify.log.warn({
          domain: targetDomain,
          verified: domainConfig.verified // Corrected: Prisma model likely uses 'verified'
        }, 'Email rejected - domain not verified');
        
        return reply.code(403).send({
          statusCode: 403,
          error: 'Forbidden',
          message: 'Domain not verified. Email processing is disabled until domain ownership is verified.',
          verificationStatus: domainConfig.verificationStatus,
        });
      }

      // Check user's monthly email limit
      const canProcessEmail = await checkUserUsageLimit(domainConfig.user.id);
      if (!canProcessEmail) {
        fastify.log.warn({
          domain: targetDomain,
          userId: domainConfig.user.id,
          currentUsage: domainConfig.user.currentMonthEmails,
          limit: domainConfig.user.monthlyEmailLimit
        }, 'Email rejected - user exceeded monthly limit');
        
        return reply.code(429).send({
          statusCode: 429,
          error: 'Too Many Requests',
          message: 'Monthly email limit exceeded. Please upgrade your plan or wait until next month.',
          currentUsage: domainConfig.user.currentMonthEmails,
          monthlyLimit: domainConfig.user.monthlyEmailLimit,
        });
      }

      // Domain must be active
      if (!domainConfig.active) { // Assuming 'active' field exists, as per original logic
        fastify.log.info({ domain: targetDomain }, 'Email rejected - domain inactive');
        return reply.code(403).send({ statusCode: 403, error: 'Forbidden', message: 'Domain is inactive' });
      }

      // Parse the email using the enhanced parser
      const parsedEmail = await EmailParser.parseToWebhookPayload(rawEmail, targetDomain);

      // Check if this is a test webhook for web.xadi.eu
      if (targetDomain === 'web.xadi.eu') {
        const userIdSuffix = parsedEmail.message.recipient.email.split('@')[0];
        const testUser = await findUserByIdSuffix(userIdSuffix);

        if (testUser) {
          // Store webhook payload directly instead of queuing
          const emailRecord = await prisma.email.create({
            data: {
              messageId: parsedEmail.envelope.messageId,
              fromAddress: parsedEmail.message.sender.email,
              toAddresses: [parsedEmail.message.recipient.email],
              subject: parsedEmail.message.subject,
              domainId: null, // Special case for test webhooks
              webhookPayload: parsedEmail,
              isTestWebhook: true,
              deliveryStatus: 'DELIVERED',
              deliveredAt: new Date(),
              expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            },
          });

          // Emit WebSocket event for real-time update
          webSocketService.emitEmailProcessed(testUser.id, {
            messageId: parsedEmail.envelope.messageId,
            fromAddress: parsedEmail.message.sender.email,
            subject: parsedEmail.message.subject || '(no subject)',
            isTestWebhook: true,
            deliveryStatus: 'DELIVERED',
            webhookPayload: parsedEmail,
            timestamp: new Date().toISOString()
          });

          await incrementUserEmailUsage(testUser.id);

          fastify.log.info({
            messageId: parsedEmail.envelope.messageId,
            userId: testUser.id,
            testEmail: parsedEmail.message.recipient.email
          }, 'Test webhook email processed');

          return reply.code(202).send({
            success: true,
            messageId: parsedEmail.envelope.messageId,
            status: 'delivered',
            emailId: emailRecord.id,
            isTestWebhook: true
          });
        }
      }

      // Look up webhook configuration for this domain/email
      const webhookConfig = await lookupWebhookConfig(domainConfig, parsedEmail.message.recipient.email, fastify.log);

      if (!webhookConfig) {
        fastify.log.info({ domain: targetDomain, messageId: parsedEmail.envelope.messageId }, 'No webhook configured');
        return reply.code(404).send({ statusCode: 404, error: 'Not Found', message: 'No webhook configured for this email address or domain' });
      }

      // Create email record for tracking
      const emailRecord = await prisma.email.create({
        data: {
          messageId: parsedEmail.envelope.messageId,
          fromAddress: parsedEmail.message.sender.email,
          toAddresses: [parsedEmail.message.recipient.email],
          subject: parsedEmail.message.subject,
          domainId: domainConfig.id,
          expiresAt: new Date(Date.now() + (domainConfig.dataRetentionDays || 30) * 24 * 60 * 60 * 1000),
          // deliveryStatus defaults to PENDING
        },
      });

      // Queue for webhook delivery using the enhanced payload
      await queueWebhookDelivery(webhookConfig.url, parsedEmail, webhookConfig.secret);

      // Increment user's email usage count (only after successful processing)
      await incrementUserEmailUsage(domainConfig.user.id);

      // Emit WebSocket event for real-time update
      webSocketService.emitEmailProcessed(domainConfig.user.id, {
        messageId: parsedEmail.envelope.messageId,
        fromAddress: parsedEmail.message.sender.email,
        subject: parsedEmail.message.subject || '(no subject)',
        isTestWebhook: false,
        deliveryStatus: 'DELIVERED', // Will be updated by queue processor
        timestamp: new Date().toISOString()
      });

      fastify.log.info({
        messageId: parsedEmail.envelope.messageId,
        domain: targetDomain,
        webhookUrl: webhookConfig.url,
        hasSecret: !!webhookConfig.secret,
        emailRecordId: emailRecord.id,
        userId: domainConfig.user.id
      }, 'Email processed and queued for webhook delivery');

      // Changed to 202 as per OpenAPI spec
      return reply.code(202).send({
        success: true,
        messageId: parsedEmail.envelope.messageId,
        status: 'queued',
        emailId: emailRecord.id,
      });

    } catch (error: any) {
      fastify.log.error({ error: error.message, stack: error.stack }, 'Failed to process email');
      return reply.code(500).send({ statusCode: 500, error: 'Internal Server Error', message: 'Failed to process email' });
    }
  });

  // Get email processing status
  fastify.get('/status/:messageId', {
    schema: {
      description: 'Get the processing and delivery status of a specific email.',
      tags: ['Webhook Operations'], // A new tag, or could be 'Webhook' or 'Admin'
      summary: 'Get email delivery status',
      params: {
        type: 'object',
        properties: {
          messageId: { type: 'string', description: 'The unique message ID of the email.' },
        },
        required: ['messageId'],
      },
      response: {
        200: {
          description: 'Detailed status of the email.',
          type: 'object',
          properties: {
            messageId: { type: 'string' },
            status: { type: 'string', enum: ['queued', 'sent', 'delivered', 'failed', 'deferred'] }, // Example statuses
            deliveryAttempts: { type: 'integer' },
            lastAttempt: { type: 'string', format: 'date-time', nullable: true },
            deliveredAt: { type: 'string', format: 'date-time', nullable: true },
            errorMessage: { type: 'string', nullable: true },
            domain: { type: 'string' },
            webhookUrl: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            // expiresAt: { type: 'string', format: 'date-time' }, // If still relevant
          },
        },
        404: { description: 'Email not found', ...errorResponseSchema },
        500: { description: 'Failed to retrieve email status', ...errorResponseSchema },
      },
    },
  }, async (request, reply) => {
    const { messageId } = request.params as { messageId: string };
    
    try {
      // Get email status with domain info
      const emailRecord = await prisma.email.findUnique({
        where: { messageId },
        include: {
          domain: {
            select: { domain: true, webhook: { select: { url: true, name: true } } }
          }
        },
      });
      
      if (!emailRecord) {
        return reply.code(404).send({ statusCode: 404, error: 'Not Found', message: 'Email not found' });
      }
      
      // Return email status with webhook info
      return reply.send({
        messageId,
        status: emailRecord.deliveryStatus.toLowerCase(),
        deliveryAttempts: emailRecord.deliveryAttempts,
        lastAttempt: emailRecord.lastAttemptAt?.toISOString(),
        deliveredAt: emailRecord.deliveredAt?.toISOString(),
        errorMessage: emailRecord.errorMessage,
        domain: emailRecord.domain.domain,
        webhookUrl: emailRecord.domain.webhook?.url,
        webhookName: emailRecord.domain.webhook?.name,
        createdAt: emailRecord.createdAt.toISOString(),
        expiresAt: emailRecord.expiresAt.toISOString(),
      });
      
    } catch (error: any) {
      fastify.log.error({ messageId, error: error.message, stack: error.stack }, 'Failed to get email status');
      
      return reply.code(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Failed to retrieve email status',
        // details: error.message, // Optionally include for debugging, but be careful in prod
      });
    }
  });
};

/**
 * Extract domain from raw email headers before full parsing
 * This is more efficient and handles the case where parsing might fail
 */
function extractDomainFromRawEmail(rawEmail: string | Buffer): string | null {
  const emailText = typeof rawEmail === 'string' ? rawEmail : rawEmail.toString();
  
  // Look for common email headers that contain the recipient
  const patterns = [
    /^X-Original-To:\s*.*@([^\s\r\n]+)/im,
    /^Delivered-To:\s*.*@([^\s\r\n]+)/im,
    /^To:\s*.*@([^\s\r\n]+)/im,
    /^Envelope-To:\s*.*@([^\s\r\n]+)/im,
  ];
  
  for (const pattern of patterns) {
    const match = emailText.match(pattern);
    if (match && match[1]) {
      return match[1].toLowerCase().trim();
    }
  }
  
  // Fallback: use basic domain extraction from To header
  const toMatch = emailText.match(/^To:\s*(.+)$/im);
  if (toMatch) {
    const emailAddr = toMatch[1].match(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/);
    if (emailAddr) {
      // Extract domain using basic string manipulation instead of EmailParser method
      const atIndex = emailAddr[0].indexOf('@');
      if (atIndex !== -1) {
        return emailAddr[0].substring(atIndex + 1).toLowerCase();
      }
    }
  }
  
  return null;
}

/**
 * Look up webhook configuration for domain/email combination
 * Checks aliases first, then falls back to domain default webhook
 */
async function lookupWebhookConfig(
  domainConfig: any,
  emailAddress: string,
  log: FastifyBaseLogger
): Promise<{ url: string; secret?: string } | null> {
  // 1. Check for specific alias configuration first
  const alias = domainConfig.aliases.find((alias: any) =>
    alias.email === emailAddress && alias.active && alias.webhook
  );

  if (alias && alias.webhook) {
    log.debug({
      emailAddress,
      webhookUrl: alias.webhook.url,
      webhookName: alias.webhook.name,
      hasSecret: !!alias.webhook.webhookSecret
    }, 'Using alias-specific webhook');
    return {
      url: alias.webhook.url,
      secret: alias.webhook.webhookSecret || undefined
    };
  }

  // 2. Fall back to domain's default webhook
  if (domainConfig.active && domainConfig.webhook) {
    log.debug({
      domain: domainConfig.domain,
      webhookUrl: domainConfig.webhook.url,
      webhookName: domainConfig.webhook.name,
      hasSecret: !!domainConfig.webhook.webhookSecret
    }, 'Using domain default webhook');
    return {
      url: domainConfig.webhook.url,
      secret: domainConfig.webhook.webhookSecret || undefined
    };
  }

  // 3. No webhook configured
  log.warn({
    domain: domainConfig.domain,
    emailAddress,
    aliasCount: domainConfig.aliases.length
  }, 'No webhook configured for email or domain');

  return null;
}

/**
 * Find user by the last 8 characters of their ID
 */
async function findUserByIdSuffix(suffix: string): Promise<{ id: string } | null> {
  if (suffix.length !== 8) {
    return null;
  }

  try {
    // Find user where ID ends with the suffix
    const user = await prisma.user.findFirst({
      where: {
        id: {
          endsWith: suffix
        }
      },
      select: {
        id: true
      }
    });

    return user;
  } catch (error) {
    return null;
  }
}