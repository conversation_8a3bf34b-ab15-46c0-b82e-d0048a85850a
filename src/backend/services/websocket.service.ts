import { FastifyInstance } from 'fastify';
import { Server as SocketIOServer } from 'socket.io';
import { logger } from '../utils/logger.js';
import { verifyJWT } from '../lib/auth.js';

interface AuthenticatedSocket {
  userId: string;
  email: string;
}

interface EmailProcessedEvent {
  type: 'email_processed';
  data: {
    messageId: string;
    fromAddress: string;
    subject: string;
    isTestWebhook: boolean;
    deliveryStatus: 'DELIVERED' | 'FAILED';
    webhookPayload?: any;
    timestamp: string;
  };
}

interface MetricsUpdatedEvent {
  type: 'metrics_updated';
  data: {
    domains: number;
    aliases: number;
    webhooks: number;
    emails: number;
  };
}

type WebSocketEvent = EmailProcessedEvent | MetricsUpdatedEvent;

class WebSocketService {
  private io: SocketIOServer | null = null;
  private userSockets = new Map<string, Set<string>>(); // userId -> Set of socketIds

  initialize(fastify: FastifyInstance) {
    this.io = new SocketIOServer(fastify.server, {
      cors: {
        origin: process.env.NODE_ENV === 'development' ? "http://localhost:3000" : false,
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = await verifyJWT(token);
        if (!decoded) {
          return next(new Error('Invalid authentication token'));
        }

        // Attach user info to socket
        (socket as any).user = {
          userId: decoded.id,
          email: decoded.email
        };

        next();
      } catch (error) {
        logger.error({ error: error.message }, 'WebSocket authentication failed');
        next(new Error('Authentication failed'));
      }
    });

    this.io.on('connection', (socket) => {
      const user = (socket as any).user as AuthenticatedSocket;
      
      logger.info({ 
        userId: user.userId, 
        socketId: socket.id 
      }, 'User connected to WebSocket');

      // Track user socket
      if (!this.userSockets.has(user.userId)) {
        this.userSockets.set(user.userId, new Set());
      }
      this.userSockets.get(user.userId)!.add(socket.id);

      // Join user-specific room
      socket.join(`user:${user.userId}`);

      // Handle disconnection
      socket.on('disconnect', () => {
        logger.info({ 
          userId: user.userId, 
          socketId: socket.id 
        }, 'User disconnected from WebSocket');

        // Remove socket from tracking
        const userSocketSet = this.userSockets.get(user.userId);
        if (userSocketSet) {
          userSocketSet.delete(socket.id);
          if (userSocketSet.size === 0) {
            this.userSockets.delete(user.userId);
          }
        }
      });

      // Handle ping/pong for connection health
      socket.on('ping', () => {
        socket.emit('pong');
      });
    });

    logger.info('WebSocket service initialized');
  }

  /**
   * Emit event to specific user
   */
  emitToUser(userId: string, event: WebSocketEvent) {
    if (!this.io) {
      logger.warn('WebSocket service not initialized');
      return;
    }

    this.io.to(`user:${userId}`).emit(event.type, event.data);
    
    logger.debug({ 
      userId, 
      eventType: event.type,
      connectedSockets: this.userSockets.get(userId)?.size || 0
    }, 'Event emitted to user');
  }

  /**
   * Emit email processed event
   */
  emitEmailProcessed(userId: string, emailData: EmailProcessedEvent['data']) {
    this.emitToUser(userId, {
      type: 'email_processed',
      data: emailData
    });
  }

  /**
   * Emit metrics updated event
   */
  emitMetricsUpdated(userId: string, metrics: MetricsUpdatedEvent['data']) {
    this.emitToUser(userId, {
      type: 'metrics_updated',
      data: metrics
    });
  }

  /**
   * Get connected users count
   */
  getConnectedUsersCount(): number {
    return this.userSockets.size;
  }

  /**
   * Get user connection status
   */
  isUserConnected(userId: string): boolean {
    return this.userSockets.has(userId) && this.userSockets.get(userId)!.size > 0;
  }

  /**
   * Broadcast to all connected users (admin use)
   */
  broadcast(event: WebSocketEvent) {
    if (!this.io) {
      logger.warn('WebSocket service not initialized');
      return;
    }

    this.io.emit(event.type, event.data);
    logger.debug({ eventType: event.type }, 'Event broadcasted to all users');
  }
}

export const webSocketService = new WebSocketService();
